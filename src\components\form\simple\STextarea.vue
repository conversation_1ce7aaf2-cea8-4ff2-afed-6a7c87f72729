<script setup lang="ts">
import type { TextareaProps } from 'primevue'
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  textareaProps?: TextareaProps
}>()

const { value, errorMessage } = useField<string | null | undefined>(() => props.name)
</script>

<template>
  <Textarea :id="props.name" v-model="value" :invalid="errorMessage ? true : false" fluid v-bind="textareaProps" />
  <ErrorMsg :error-message="errorMessage" class="mt-1" />
</template>
