{"compilerOptions": {"target": "es2017", "jsx": "preserve", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "paths": {"~/*": ["src/*"]}, "resolveJsonModule": true, "types": ["vite/client"], "allowJs": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "exclude": ["dist", "node_modules", "eslint.config.js"]}