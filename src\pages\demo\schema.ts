import { toTypedSchema } from '@vee-validate/zod'
import type { Pageable } from '~/api/common/types'

export enum DemoEnum {
  ENUM1 = 'ENUM1',
  ENUM2 = 'ENUM2',
  ENUM3 = 'ENUM3',
}
export enum TargetEnum {
  SELF = '_self',
  BLANK = '_blank',
  PARENT = '_parent',
}

export const demoOptions = [
  {
    label: '选项1',
    value: 'ENUM1',
  },
  {
    label: '选项2',
    value: 'ENUM2',
  },
  {
    label: '选项3',
    value: 'ENUM3',
  },
]

export interface DemoItem {
  label: string
  date: Date
  option: DemoEnum
}

export interface DemoData {
  name: string
  description?: string
  date1: Date
  date2?: Date
  option1: DemoEnum
  option2?: DemoEnum
  radio: DemoEnum
  yesno: boolean
  time: Date
  items: DemoItem[]
}

export interface DemoDataSearch {
  name?: string
  date1?: Date
  option1?: DemoEnum
}

export interface DemoDataWithId extends DemoData {
  id: string
}

export const demoSchema = toTypedSchema(z.object({
  name: z.string().min(1, '不可为空'),
  description: z.string().nullish(),
  date1: z.date(),
  date2: z.date().nullish(),
  option1: z.nativeEnum(DemoEnum),
  option2: z.nativeEnum(TargetEnum).nullish(),
  radio: z.nativeEnum(DemoEnum),
  yesno: z.boolean(),
  time: z.date(),
  items: z.array(z.object({
    label: z.string(),
    date: z.date(),
    option: z.nativeEnum(DemoEnum),
  })).default([]),
}))

export const demoSearchSchema = toTypedSchema(
  z.object({
    name: z.string().min(1),
    option1: z.nativeEnum(DemoEnum).optional(),
    date1: z.date().optional(),
  }),
)

export function useDemoForm() {
  const demoForm = useForm<DemoData>({
    validationSchema: demoSchema,
  })
  return demoForm
}

export const demoApi = {
  get,
  create,
  del,
  update,
  page,
}

function openDB() {
  return new Promise<IDBDatabase>((resolve, reject) => {
    const req = indexedDB.open('demo', 1)
    req.onupgradeneeded = (ev) => {
      const db = (ev.target as IDBOpenDBRequest).result
      if (!db.objectStoreNames.contains('demo')) {
        db.createObjectStore('demo', { keyPath: 'id', autoIncrement: true })
      }
    }

    req.onsuccess = (event) => {
      resolve((event.target as IDBOpenDBRequest).result)
    }
    req.onerror = (event) => {
      reject(event)
    }
  })
}

let db: IDBDatabase | null = null

async function getDB() {
  if (db) {
    return db
  }
  else {
    db = await openDB()
    return db
  }
}

async function page(
  param: Pageable<DemoDataSearch>,
) {
  info(JSON.stringify(param))
  const pageData = param.pageData
  const start = pageData.pageNumber * pageData.pageSize
  const end = (pageData.pageNumber + 1) * pageData.pageSize
  const db = await getDB()
  return new Promise<{
    data: DemoDataWithId[]
    total: number
  }>((resolve, reject) => {
    const tx = db.transaction(['demo'], 'readonly')
    const store = tx.objectStore('demo')
    const req = store.getAll()
    req.onsuccess = () => resolve(
      { data: req.result.slice(start, end) as DemoDataWithId[], total: req.result.length },
    )
    req.onerror = ev => reject(ev)
  })
}

async function get(id: string) {
  const db = await getDB()
  return new Promise<DemoDataWithId>((resolve, reject) => {
    const tx = db.transaction(['demo'], 'readonly')
    const store = tx.objectStore('demo')
    const req = store.get(id)
    req.onsuccess = () => resolve(req.result as DemoDataWithId)
    req.onerror = ev => reject(ev)
  })
}

async function create(createParam: DemoData) {
  const db = await getDB()
  return new Promise<void>((resolve, reject) => {
    const id = crypto.randomUUID()
    const tx = db.transaction(['demo'], 'readwrite')
    const store = tx.objectStore('demo')
    const data = {
      ...createParam,
      id,
    }
    const req = store.add(data)
    req.onsuccess = () => resolve()
    req.onerror = ev => reject(ev)
  })
}

async function del(id: string) {
  const db = await getDB()
  return new Promise<void>((resolve, reject) => {
    const tx = db.transaction(['demo'], 'readwrite')
    const store = tx.objectStore('demo')
    const req = store.delete(id)
    req.onsuccess = () => resolve()
    req.onerror = ev => reject(ev)
  })
}

async function update(id: string, param: DemoData) {
  const db = await getDB()
  return new Promise<void>((resolve, reject) => {
    const tx = db.transaction(['demo'], 'readwrite')
    const store = tx.objectStore('demo')
    const data = {
      ...param,
      id,
    }
    const req = store.put(data)
    req.onsuccess = () => resolve()
    req.onerror = ev => reject(ev)
  })
}
