<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'

import { useCreateMenuItemForm } from './schema'
import { menuApi } from '~/api/common/menu'

const props = defineProps<{
  parentId?: string
}>()
const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')

const { resetForm, setFieldValue, handleSubmit } = useCreateMenuItemForm()

const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    await menuApi.create(values)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})

function onShow() {
  setFieldValue('parentId', props.parentId)
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="创建菜单" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LInput name="path" label="路径" />
        <LInput name="title" label="标题" />
        <LInput name="icon" label="图标" />
        <LDictSelect name="target" label="target" code="SYS_TARGET" />
        <LInput name="access" label="权限" />
        <LInput name="parentId" label="父菜单" />
        <LBoolRadioGroup name="redirect" label="重定向" />
        <LInputNumber name="order" label="排序" />
        <LInput name="url" label="重定向地址" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" fluid>
          保存
        </Button>
        <Button severity="secondary" fluid @click="open = false">
          取消
        </Button>
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
