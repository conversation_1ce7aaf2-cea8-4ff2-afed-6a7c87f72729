export interface IncomeExtraCostRecord {
  id: string
  departmentId: number
  departmentName?: string
  type: number
  amount: number
  startDate: string
  endDate: string
  createTime?: string
  updateTime?: string
}

export interface IncomeExtraCostRecordCreate {
  departmentId?: number
  departmentName?: string
  type?: number
  amount?: number
  startDate?: string
  endDate?: string
}

export interface IncomeExtraCostRecordUpdate {
  id: string
  departmentId?: number
  departmentName?: string
  type?: number
  amount?: number
  startDate?: Date
  endDate?: Date
}

export interface IncomeExtraCostRecordEdit {
  id: string
  departmentId?: string
  departmentName?: string
  type?: number
  amount?: number
  startDate?: Date
  endDate?: Date
}
