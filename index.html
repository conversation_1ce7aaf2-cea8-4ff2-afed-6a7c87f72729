<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
    <title>frontend next</title>
    <meta name="description" content="Opinionated Vite Starter Template" />
  </head>
  <body>
    <div id="app"></div>
    <noscript>
      <div>Please enable JavaScript to use this application.</div>
    </noscript>
    <script>
      ;(function () {
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        const setting = localStorage.getItem('color-schema') || 'auto'
        if (setting === 'dark' || (prefersDark && setting !== 'light'))
          document.documentElement.classList.toggle('dark', true)
      })()
    </script>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
