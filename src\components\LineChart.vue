<script setup lang="ts">
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'echarts/charts'
import {
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
} from 'echarts/components'
import * as echarts from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { computed } from 'vue'
import { VueEcharts } from 'vue3-echarts'
import type { MonthlyDepartmentCostAnalysisMap } from '~/api/cost/analysis/types'
import { formatAmount } from '~/utils/format'

const props = defineProps<{
  data: MonthlyDepartmentCostAnalysisMap
}>()

echarts.use([
  Line<PERSON><PERSON>,
  Bar<PERSON>hart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
  CanvasRenderer,
])

const chartOptions = computed(() => {
  const months = Object.keys(props.data).sort()
  const series = [
    {
      name: '月度成本',
      type: 'bar',
      data: months.map(month => props.data[month].periodTotalCost || 0),
      yAxisIndex: 0,
      itemStyle: {
        color: '#7CB5EC',
      },
      barWidth: '60%',
    },
    {
      name: '环比',
      type: 'line',
      data: months.map(month => props.data[month].costQoQ ? props.data[month].costQoQ * 100 : 0),
      yAxisIndex: 1,
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 2,
      },
      itemStyle: {
        color: '#847370',
      },
    },
  ]

  return {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any[]) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param) => {
          if (param.value !== null) {
            const value = param.seriesName === '环比'
              ? `${param.value.toFixed(2)}%`
              : formatAmount(param.value)
            result += `${param.marker}${param.seriesName}: ${value}<br/>`
          }
        })
        return result
      },
    },
    legend: {
      data: ['月度成本', '环比'],
      bottom: 0,
      textStyle: {
        color: '#71869d',
      },
    },
    grid: {
      top: '4%',
      left: '3%',
      right: '3%',
      bottom: '12%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: months,
      boundaryGap: false,
      axisLabel: {
        color: '#71869d',
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '成本',
        nameLocation: 'end',
        nameTextStyle: {
          color: '#71869d',
          padding: [0, 0, 0, 50],
        },
        axisLabel: {
          color: '#71869d',
          formatter: (value: number) => formatAmount(value),
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#ddd',
          },
        },
      },
      {
        type: 'value',
        name: '环比(%)',
        nameLocation: 'end',
        offset: 15,
        nameTextStyle: {
          color: '#71869d',
          padding: [0, 0, 0, 50],
        },
        axisLabel: {
          color: '#71869d',
          formatter: '{value}%',
          // align: 'left',
          // margin: 30,
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series,
  }
})
</script>

<template>
  <div>
    <VueEcharts
      :option="chartOptions"
      :autoresize="true"
      style="height: 100%; width: 100%;"
    />
  </div>
</template>
