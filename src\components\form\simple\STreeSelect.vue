<script setup lang="ts">
import type { TreeSelectProps } from 'primevue'
import type { TreeNode } from 'primevue/treenode'
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  options: TreeNode[]
  selectProps?: TreeSelectProps
}>()

const { value: fieldValue, errorMessage } = useField<string | null | undefined>(() => props.name)

const invalid = computed(() => !!errorMessage.value)

const treeValue = computed<{ [key: string]: boolean }>({
  get: () => {
    if (!fieldValue.value)
      return {}
    return { [fieldValue.value]: true }
  },
  set: (val: { [key: string]: boolean }) => {
    const keys = Object.keys(val)
    if (keys.length > 0) {
      fieldValue.value = keys[0] // 确保赋值的是 string 类型
    }
  },
})
</script>

<template>
  <TreeSelect
    :id="props.name"
    v-model="treeValue"
    :options="props.options"
    :invalid="invalid"
    fluid
    v-bind="props.selectProps"
  />
  <ErrorMsg :error-message="errorMessage" class="mt-1" />
</template>
