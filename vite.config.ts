/// <reference types="vitest" />

import path from 'node:path'
import process from 'node:process'
import type { ConfigEnv, UserConfig } from 'vite'
import { loadEnv } from 'vite'

import { plugins } from './plugins'

export default ({ mode }: ConfigEnv): UserConfig => {
  const env = loadEnv(mode, process.cwd())
  return {
    resolve: {
      alias: {
        '~/': `${path.resolve(__dirname, 'src')}/`,
      },
    },

    plugins,
    // https://github.com/vitest-dev/vitest
    test: {
      environment: 'jsdom',
    },

    build: {
      chunkSizeWarningLimit: 4096,
      rollupOptions: {
        output: {
          manualChunks: {
            vue: ['vue', 'vue-router', 'pinia', '@vueuse/core'],
            primevue: ['primevue'],
          },
        },
      },
    },
    server: {
      proxy: {
        [env.VITE_APP_BASE_API]: {
          target: env.VITE_APP_BASE_URL,
          changeOrigin: true,
          rewrite: path => path.replace(new RegExp(`^${env.VITE_APP_BASE_API}`), '/'),
        },
      },
    },
  }
}
