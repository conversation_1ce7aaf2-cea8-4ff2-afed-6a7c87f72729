<script setup lang="ts">
import type { DatePickerProps } from 'primevue'
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  datePickerProps?: DatePickerProps
}>()
const { value, errorMessage } = useField<Date | Array<Date> | Array<Date | null> | undefined | null>(props.name)
</script>

<template>
  <DatePicker v-model="value" :input-id="props.name" fluid show-icon date-format="yy-mm-dd" v-bind="datePickerProps" :invalid="errorMessage ? true : false">
    <template #inputicon="slotProps">
      <i class="pi pi-clock" @click="slotProps.clickCallback" />
    </template>
  </DatePicker>
  <ErrorMsg :error-message="errorMessage" />
</template>
