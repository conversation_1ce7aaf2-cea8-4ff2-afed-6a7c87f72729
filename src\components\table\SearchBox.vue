<script setup lang="ts">
import But<PERSON> from 'primevue/button'

defineProps<{
  loading?: boolean
}>()
const resetForm = useResetForm()
</script>

<template>
  <form class="px-8">
    <div grid grid-cols-1 gap-x-8 gap-y-2 md:grid-cols-4 sm:grid-cols-3 xl:grid-cols-6>
      <slot />
    </div>
    <div class="mt-4 w-full flex gap-4 flex-justify-end">
      <Button label="查询" icon="pi pi-search" type="submit" :loading="loading" />
      <Button label="重置" icon="pi pi-eraser" severity="secondary" outlined @click="resetForm()" />
    </div>
  </form>
</template>
