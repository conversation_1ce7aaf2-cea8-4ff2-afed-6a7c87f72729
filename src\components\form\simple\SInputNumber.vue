<script setup lang="ts">
import type { InputNumberProps } from 'primevue'

const props = defineProps<{
  name: string
  inputNumberProps?: InputNumberProps
}>()

const { value, errorMessage } = useField<number | null | undefined>(() => props.name)
</script>

<template>
  <InputNumber :id="props.name" v-model="value" :invalid="!!errorMessage" v-bind="inputNumberProps" />
  <ErrorMsg :error-message="errorMessage" />
</template>
