<script setup lang="ts">
import type { InputTextProps } from 'primevue'
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  inputTextProps?: InputTextProps
}>()

const { value, errorMessage } = useField<string | null | undefined>(() => props.name)
</script>

<template>
  <InputText :id="props.name" v-model="value" :invalid="errorMessage ? true : false" fluid v-bind="inputTextProps" />
  <ErrorMsg :error-message="errorMessage" class="mt-1" />
</template>
