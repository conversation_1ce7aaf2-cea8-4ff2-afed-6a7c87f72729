<script setup lang="ts">
const props = defineProps<{
  name: string
  label: string
  options: {
    label: string
    value: string
  }[]
}>()

const { value, errorMessage } = useField<string | null | undefined>(() => props.name)
</script>

<template>
  <LightFormItem :name="name" :label="label">
    <div class="flex flex-wrap gap-4">
      <div v-for="option in options" :key="option.value" class="flex gap-2">
        <RadioButton v-model="value" :input-id="option.value" :name="option.label" :value="option.value" />
        <label :for="option.value">{{ option.label }}</label>
      </div>
    </div>
    <ErrorMsg :error-message="errorMessage" />
  </LightFormItem>
</template>
