<script setup lang="ts">
import type { TreeSelectionKeys } from 'primevue'
import type { TreeNode } from 'primevue/treenode'
import { departmentApi } from '~/api/cost/department'
import type { DepartmentTreeNode } from '~/api/cost/department/types'
import { useDepartmentStore } from '~/stores/department'

// 定义层级对应图标常量
const LEVEL_ICONS = [
  'pi pi-fw pi-building', // 第0级 - 公司级
  'pi pi-fw pi-sitemap', // 第1级 - 部门级
  'pi pi-fw pi-users', // 第2级 - 小组级
  'pi pi-fw pi-folder-open', // 第3级 - 文件夹级
  'pi pi-fw pi-file', // 第4级+ - 文件级
]

/**
 * 将部门数据转换为树节点
 * @param dept 部门数据节点
 * @param level 当前节点层级，默认为0
 */
function convertToTreeNode(dept: DepartmentTreeNode, level: number = 0): TreeNode {
  return {
    key: dept.id.toString(),
    label: dept.departmentname,
    data: dept.departmentmark,
    icon: LEVEL_ICONS[Math.min(level, 4)],
    children: dept.children?.map(child => convertToTreeNode(child, level + 1)),
  }
}

const nodes = ref<TreeNode[]>([])
const errorMessage = ref<string>('')
const selectedKey = ref<TreeSelectionKeys>()
const departmentStore = useDepartmentStore()

// 观察选中节点变化，更新store
watch(selectedKey, (newVal) => {
  const keys = newVal ? Object.keys(newVal) : []

  if (keys.length > 0) {
    const firstKey = keys[0]

    // 明确排除 null 的情况
    if (firstKey === null)
      return

    const numericKey = Number(firstKey)

    // 验证有效数字
    if (!Number.isNaN(numericKey) && Number.isFinite(numericKey)) {
      departmentStore.setSelectedDepartmentId(numericKey)
    }
  }
}, { deep: true })

// 加载部门树数据
async function loadDepartments() {
  try {
    const data = await departmentApi.tree()
    nodes.value = data.map(item => convertToTreeNode(item, 0))
    errorMessage.value = ''

    // 默认选中第一个节点（根据需求决定是否启用）
    if (nodes.value.length > 0 && !selectedKey.value) {
      selectedKey.value = { [nodes.value[0].key]: true }
    }
  }
  catch (error) {
    console.error('加载组织机构失败:', error)
    errorMessage.value = '部门数据加载失败，请尝试刷新页面'
  }
}

loadDepartments()
</script>

<template>
  <div class="department-tree">
    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">
      <i class="pi pi-exclamation-triangle" />
      {{ errorMessage }}
    </div>

    <!-- 树形组件 -->
    <Tree
      v-model:selection-keys="selectedKey"
      :value="nodes"
      selection-mode="single"
    >
      <template #node="slotProps">
        <div class="node-container">
          <i :class="slotProps.node.icon" />
          <span class="node-label">{{ slotProps.node.label }}</span>
        </div>
      </template>
    </Tree>
  </div>
</template>

<style scoped>
.department-tree {
  padding: 0.5rem;
}

.error-message {
  color: #e24c4c;
  padding: 0.5rem;
  background: #ffe3e3;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.node-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.25rem 0;
}

.node-label {
  font-size: 0.9rem;
}
</style>
