<script setup lang="ts">
import { ref } from 'vue'

import { presets, primaryColors, surfaces } from '~/config/layout'
import { useLayoutStore } from '~/stores/layout'

const { layoutConfig, updateMenuMode, changePreset, updatePrimary, updateSurface } = useLayoutStore()
const dark = useDark()
const preset = ref(layoutConfig.preset)
const presetOptions = ref(Object.keys(presets))

const menuMode = ref(layoutConfig.menuMode)
const menuModeOptions = ref([
  { label: 'Static', value: 'static' },
  { label: 'Overlay', value: 'overlay' },
])

const primaryButtonClass = function (primary: string) {
  return layoutConfig.primary === primary ? 'outline outline-1 outline-primary outline-offset-1' : 'outline-none'
}

const surfaceButtonClass = function (surface: string) {
  return (layoutConfig.surface ? layoutConfig.surface === surface : dark ? surface === 'zinc' : surface === 'slate')
    ? 'outline outline-1 outline-surface outline-offset-1'
    : 'outline-none'
}

watch(preset, (newValue) => {
  changePreset(newValue)
})

watch(menuMode, (newValue) => {
  updateMenuMode(newValue)
})
</script>

<template>
  <div
    class="config-panel absolute right-0 top-[3.25rem] hidden w-64 origin-top border rounded bg-surface-0 p-4 shadow-[0px_3px_5px_rgba(0,0,0,0.02),0px_0px_2px_rgba(0,0,0,0.05),0px_1px_4px_rgba(0,0,0,0.08)] border-surface dark:bg-surface-900"
  >
    <div class="flex flex-col gap-4">
      <div>
        <span class="text-sm font-semibold text-muted-color">Primary</span>
        <div class="flex flex-wrap justify-between gap-2 pt-2">
          <button
            v-for="primaryColor of primaryColors"
            :key="primaryColor.name"
            type="button"
            :title="primaryColor.name"
            class="h-5 w-5 cursor-pointer rounded-full border-none p-0"
            :class="primaryButtonClass(primaryColor.name)"
            :style="{ backgroundColor: `${primaryColor.name === 'noir' ? 'var(--text-color)' : primaryColor.palette['500']}` }"
            @click="updatePrimary(primaryColor)"
          />
        </div>
      </div>
      <div>
        <span class="text-sm text-muted font-semibold">Surface</span>
        <div class="flex flex-wrap justify-between gap-2 pt-2">
          <button
            v-for="surface of surfaces"
            :key="surface.name"
            type="button"
            :title="surface.name"
            class="h-5 w-5 cursor-pointer rounded-full border-none p-0"
            :class="surfaceButtonClass(surface.name)"
            :style="{ backgroundColor: `${surface.palette['500']}` }"
            @click="updateSurface(surface)"
          />
        </div>
      </div>
      <div class="flex flex-col gap-2">
        <span class="text-sm font-semibold text-muted-color">Presets</span>
        <SelectButton v-model="preset" :options="presetOptions" :allow-empty="false" />
      </div>
      <div class="flex flex-col gap-2">
        <span class="text-sm font-semibold text-muted-color">Menu Mode</span>
        <SelectButton v-model="menuMode" :options="menuModeOptions" :allow-empty="false" option-label="label" option-value="value" />
      </div>
    </div>
  </div>
</template>
