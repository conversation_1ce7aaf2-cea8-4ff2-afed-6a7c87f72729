import { type RouteRecordRaw, createRouter, createWebHistory } from 'vue-router'

import { globalGuard } from './router-guard'
import commonRoute from './common'
import adminRoute from './admin'
import demoRoute from './demo'

function buildRoute(routes: RouteRecordRaw[][]) {
  const result = routes.flatMap(o => o)
  return result
}

const routes = buildRoute([commonRoute, adminRoute, demoRoute])
const layoutRoutes = [
  {
    path: '/',
    name: 'layout',
    component: () => import('~/layout/AppLayout.vue'),
    meta: {

    },
    children: routes.filter(o => !o.meta?.noLayout),
  },
  ...routes.filter(o => o.meta?.noLayout),
] satisfies RouteRecordRaw[]
const router = createRouter({
  routes: layoutRoutes,
  history: createWebHistory(import.meta.env.BASE_URL),
})

globalGuard(router)
export { router }
