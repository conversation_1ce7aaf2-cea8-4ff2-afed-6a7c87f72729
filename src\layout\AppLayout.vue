<script setup lang="ts">
import { storeToRefs } from 'pinia'
import AppSidebar from './AppSidebar.vue'
import AppTopbar from './AppTopbar.vue'
import { useMenuStore } from '~/stores/menu'
import { useLayoutStore } from '~/stores/layout'

const { layoutConfig } = useLayoutStore()
const { menuState, isSidebarActive } = storeToRefs(useMenuStore())
const { resetMenu } = useMenuStore()
const outsideClickListener = ref<any | null>(null)

watch(isSidebarActive, (newVal) => {
  if (newVal) {
    bindOutsideClickListener()
  }
  else {
    unbindOutsideClickListener()
  }
})

const containerClass = computed(() => {
  return {
    'layout-overlay': layoutConfig.menuMode === 'overlay',
    'layout-static': layoutConfig.menuMode === 'static',
    'layout-static-inactive': menuState.value.staticMenuDesktopInactive && layoutConfig.menuMode === 'static',
    'layout-overlay-active': menuState.value.overlayMenuActive,
    'layout-mobile-active': menuState.value.staticMenuMobileActive,
  }
})

function bindOutsideClickListener() {
  if (!outsideClickListener.value) {
    outsideClickListener.value = (event: PointerEvent) => {
      if (isOutsideClicked(event)) {
        resetMenu()
      }
    }
    document.addEventListener('click', outsideClickListener.value)
  }
}

function unbindOutsideClickListener() {
  if (outsideClickListener.value) {
    document.removeEventListener('click', outsideClickListener.value)
    outsideClickListener.value = null
  }
}

function isOutsideClicked(event: PointerEvent) {
  const sidebarEl = document.querySelector('.layout-sidebar')
  const topbarEl = document.querySelector('.layout-menu-button')
  if (event.target) {
    return !(sidebarEl?.isSameNode(event.target as Element) || sidebarEl?.contains(event.target as Element) || topbarEl?.isSameNode(event.target as Element) || topbarEl?.contains(event.target as Element))
  }
}
</script>

<template>
  <div class="layout-wrapper" :class="containerClass">
    <AppTopbar />
    <AppSidebar />
    <div class="layout-main-container">
      <div class="layout-main">
        <router-view v-slot="{ Component }">
          <Transition name="fade" type="animation">
            <component :is="Component" />
          </Transition>
        </router-view>
      </div>
    </div>
    <div class="layout-mask animate-fade-in" />
  </div>
</template>

<style lang="css" scoped>
.fade-enter-active {
  animation: fade-in 0.5s;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
