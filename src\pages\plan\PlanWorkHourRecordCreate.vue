<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'

import type { TreeNode } from 'primevue/treenode'
import { WorkHourTypeOptions, usePlanWorkHourCreateForm } from './schema'
import { departmentApi } from '~/api/cost/department'
import type { DepartmentTreeNode } from '~/api/cost/department/types'
import { planWorkHourRecordApi } from '~/api/cost/plan_work_hour_record'
import { success } from '~/composables/toast'

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')
const nodes = ref<TreeNode[]>([])

const { resetForm, handleSubmit } = usePlanWorkHourCreateForm()

const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    // 转换 departmentId 为数字，并查找 departmentName
    const departmentId = Number(values.departmentId)
    const departmentName = findDepartmentName(nodes.value, departmentId)

    // 确保类型定义允许这些字段（根据接口定义可能需要调整）
    const payload = {
      ...values,
      departmentId,
      departmentName,
    }

    await planWorkHourRecordApi.create(payload)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})

function onShow() {
  resetForm()
}

// 定义层级对应图标常量
const LEVEL_ICONS = [
  'pi pi-fw pi-building', // 第0级 - 公司级
  'pi pi-fw pi-sitemap', // 第1级 - 部门级
  'pi pi-fw pi-users', // 第2级 - 小组级
  'pi pi-fw pi-folder-open', // 第3级 - 文件夹级
  'pi pi-fw pi-file', // 第4级+ - 文件级
]

function convertToTreeNode(dept: DepartmentTreeNode, level: number = 0): TreeNode {
  return {
    key: dept.id.toString(),
    label: dept.departmentname,
    data: dept.departmentmark,
    icon: LEVEL_ICONS[Math.min(level, 4)],
    children: dept.children?.map(child => convertToTreeNode(child, level + 1)),
  }
}

function findDepartmentName(nodes: TreeNode[], id: number): string | undefined {
  for (const node of nodes) {
    if (node.key === id.toString()) {
      return node.label
    }
    if (node.children) {
      const name = findDepartmentName(node.children, id)
      if (name)
        return name
    }
  }
  return undefined
}

// 加载部门树数据
async function loadDepartments() {
  try {
    const data = await departmentApi.tree()
    nodes.value = data.map(item => convertToTreeNode(item, 0))
  }
  catch (error) {
    console.error('加载组织机构失败:', error)
  }
}

onMounted(() => {
  loadDepartments()
})
</script>

<template>
  <Dialog v-model:visible="open" modal header="创建计划工时记录" @show="onShow">
    <form @submit.prevent="save">
      <FormLayout>
        <LTreeSelect name="departmentId" label="部门名称" :options="nodes" :select-props="{ placeholder: '请选择部门' }" />
        <LSelect name="type" label="员工类型" :options="WorkHourTypeOptions" :select-props="{ placeholder: '请选择员工类型' }" />
        <LDatePicker name="date" label="日期" />
        <LInputNumber
          name="planWorkHour" label="计划工时"
          :input-number-props="{
            placeholder: '请输入计划工时',
            mode: 'decimal',
            minFractionDigits: 0,
            maxFractionDigits: 2,
            step: 0.5,
          }"
        />
      </FormLayout>

      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" :loading="loading" fluid icon="pi pi-save" label="保存" />
        <Button severity="secondary" fluid icon="pi pi-times" label="取消" @click="open = false" />
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
