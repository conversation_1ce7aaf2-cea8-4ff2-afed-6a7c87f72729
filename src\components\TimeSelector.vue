<!-- components/TimeSelector.vue -->
<script setup lang="ts">
import { defineEmits, defineProps } from 'vue'

interface TimeOption {
  value: string
  label: string
}

const props = defineProps<{
  modelValue: string
  options: TimeOption[]
}>()

const emit = defineEmits(['update:modelValue'])

const selectedValue = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})
</script>

<template>
  <div class="time-selector">
    <select v-model="selectedValue">
      <option
        v-for="option in options"
        :key="option.value"
        :value="option.value"
      >
        {{ option.label }}
      </option>
    </select>
  </div>
</template>

<style scoped>
.time-selector {
  margin-bottom: 32px;
}

select {
  background: white
    url("data:image/svg+xml,%3Csvg width='12' height='8' viewBox='0 0 12 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 1.5L6 6.5L11 1.5' stroke='%232d3a4e' stroke-width='1.5'/%3E%3C/svg%3E")
    no-repeat right 16px center;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 10px 48px 10px 16px;
  appearance: none;
  font-size: 1rem;
  width: 240px;
}
</style>
