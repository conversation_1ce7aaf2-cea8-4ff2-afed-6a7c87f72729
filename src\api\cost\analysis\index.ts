import type { CostAnalysisQueryParams, DepartmentCostAnalysis, MonthlyDepartmentCostAnalysisMap } from './types'
import { kyGet } from '~/utils/request'

export const analysisApi = {
  // 成本分析指标
  board: (params?: CostAnalysisQueryParams) =>
    kyGet('costAnalysis/board', params).json<{ data: DepartmentCostAnalysis }>().then(res => res.data),
  monthly: (params?: CostAnalysisQueryParams) =>
    kyGet('costAnalysis/monthly', params).json<{ data: MonthlyDepartmentCostAnalysisMap }>().then(res => res.data),
}
