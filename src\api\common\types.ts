export interface PageData {
  pageNumber: number
  pageSize: number
}

export interface PageList<T> {
  total: number
  list: T[]
}

export interface Pageable<T> {
  pageData: PageData
  searchParams: T
}

export interface Id {
  id: string
}

export type WithId<T> = Id & T

// 分页请求参数
export interface PageRequest<T> {
  pageNumber: number
  pageSize: number
  searchParams: T
}

// 通用返回结果
export interface CommonResult<T> {
  code: number
  message: string
  data: T
}

export interface PageResult<T> {
  pageNum: number
  pageSize: number
  totalSize: number
  totalPages: number
  list: T[]
}
