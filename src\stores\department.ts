import { defineStore } from 'pinia'

export const useDepartmentStore = defineStore('department', {
  state: () => ({
    selectedDepartmentId: 191,
  }),
  actions: {
    setSelectedDepartmentId(id: number) {
      this.selectedDepartmentId = id
    },
  },
  // persist: {
  //   key: 'department-store', // 自定义存储的key
  //   storage: sessionStorage, // 使用 sessionStorage 而不是 localStorage
  //   paths: ['selectedDepartmentId'], // 只持久化特定字段
  // },
})
