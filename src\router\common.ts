import type { RouteRecordRaw } from 'vue-router'

export default [
  {
    path: '/',
    component: () => import('~/pages/index.vue'),
    name: 'home',
    meta: {
    },
  },
  {
    path: '/login',
    component: () => import('~/pages/auth/Login.vue'),
    name: 'login',
    meta: {
      isPublic: true,
      noLayout: true,
    },
  },
  {
    path: '/401',
    component: () => import('~/pages/error/401.vue'),
    meta: {
      isPublic: true,
      noLayout: true,
    },
  },
  {
    path: '/403',
    component: () => import('~/pages/error/403.vue'),
    meta: {
      isPublic: true,
      noLayout: true,
    },
  },
  {
    path: '/500',
    component: () => import('~/pages/error/500.vue'),
    meta: {
      isPublic: true,
      noLayout: true,
    },
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('~/pages/error/404.vue'),
    meta: {
      isPublic: true,
      noLayout: true,
    },
  },

] satisfies RouteRecordRaw[]
