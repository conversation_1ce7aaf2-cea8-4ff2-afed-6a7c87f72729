import type { IncomeExtraCostRecord, IncomeExtraCostRecordCreate, IncomeExtraCostRecordUpdate } from './types'
import type { CommonResult, PageRequest, PageResult } from '~/api/common/types'
import { kyCreate, kyDelete, kyGet, kyPost, kyPut } from '~/utils/request'

export const incomeExtraCostRecordApi = {
  page: (param: PageRequest<Partial<IncomeExtraCostRecord>>) => kyPost('incomeExtraCostRecord/page', param).json<CommonResult<PageResult<IncomeExtraCostRecord>>>(),
  create: (data: IncomeExtraCostRecordCreate) => kyCreate('incomeExtraCostRecord', data),
  update: (data: IncomeExtraCostRecordUpdate) => kyPut('incomeExtraCostRecord', data),
  delete: (id: string) => kyDelete(`incomeExtraCostRecord/${id}`),
  get: (id: string) => kyGet(`incomeExtraCostRecord/${id}`).json<CommonResult<IncomeExtraCostRecord>>(),
  getByCode: (code: string) => kyGet(`dict/getByCode/${code}`).json<IncomeExtraCostRecord>(),
}
