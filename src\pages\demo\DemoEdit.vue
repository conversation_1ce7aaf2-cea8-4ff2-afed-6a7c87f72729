<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'

import { demoApi, demoOptions, useDemoForm } from './schema'

const props = defineProps<{
  id?: string
}>()

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')

const { resetForm, setValues, handleSubmit } = useDemoForm()
const { push, remove, fields } = useFieldArray('items')

const save = handleSubmit(async (values) => {
  if (props.id) {
    try {
      loading.value = true
      await demoApi.update(props.id, values)
      success('更新成功')
      emits('save')
      open.value = false
      resetForm()
    }
    finally {
      loading.value = false
    }
  }
})

async function onShow() {
  resetForm()
  if (props.id) {
    try {
      loading.value = true
      const data = await demoApi.get(props.id)
      setValues(data)
    }
    finally {
      loading.value = false
    }
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="更新demo" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LInput name="name" label="名称" />
        <LInput name="description" label="描述" />
        <LDatePicker name="date1" label="日期1" />
        <LDatePicker name="date2" label="日期2" />
        <LSelect name="option1" label="选项1" :options="demoOptions" />
        <LDictSelect name="option2" label="选项2" code="SYS_TARGET" />
        <LRadioGroup name="radio" label="选项3" :options="demoOptions" />
        <LBoolRadioGroup name="yesno" label="是否" />
        <LDatePicker :date-picker-props="{ timeOnly: true }" name="time" label="时间" />
      </FormLayout>
      <Fieldset legend="子列表">
        <DataTable :value="fields">
          <Column header="名称">
            <template #body="slotProps">
              <SInput :name="`items[${slotProps.index}].label`" />
            </template>
          </Column>
          <Column header="选项1">
            <template #body="slotProps">
              <SSelect :name="`items[${slotProps.index}].option`" :options="demoOptions" />
            </template>
          </Column>
          <Column header="日期">
            <template #body="slotProps">
              <SDatePicker :name="`items[${slotProps.index}].date`" />
            </template>
          </Column>
          <Column header="操作">
            <template #body="slotProps">
              <Button outlined severity="danger" icon="pi pi-trash" @click="remove(slotProps.index)" />
            </template>
          </Column>
        </DataTable>
        <Button size="large" outlined fluid class="pi pi-plus mt-4" @click="push({})" />
      </Fieldset>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" :loading="loading" fluid icon="pi pi-save" label="保存" />
        <Button severity="secondary" fluid icon="pi pi-times" label="取消" @click="open = false" />
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
