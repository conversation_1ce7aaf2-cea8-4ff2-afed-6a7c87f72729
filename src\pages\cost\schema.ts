import { toTypedSchema } from '@vee-validate/zod'
import type { IncomeExtraCostRecordCreate, IncomeExtraCostRecordEdit } from '~/api/cost/income_extra_cost_record/types'

// 收支类型
export enum CostTypeEnum {
  INCOME = 1,
  EXPENSE = 2,
}

export const CostTypeOptions = [
  {
    label: '收入',
    value: 1,
  },
  {
    label: '支出',
    value: 2,
  },
]

export const costSearchSchema = toTypedSchema(
  z.object({
    departmentId: z.string().optional(),
    type: z.nativeEnum(CostTypeEnum).optional(),
  }),
)

export const costSchema = toTypedSchema(
  z.object({
    departmentId: z.string().min(1, '不可为空'),
    type: z.nativeEnum(CostTypeEnum),
    amount: z.number().min(0, '金额必须大于0'),
    startDate: z.date(),
    endDate: z.date(),
  }),
)

export function useCostCreateForm() {
  const costForm = useForm<IncomeExtraCostRecordCreate>({
    validationSchema: costSchema,
  })
  return costForm
}

export function useCostEditEditForm() {
  const costForm = useForm<IncomeExtraCostRecordEdit>({
    validationSchema: costSchema,
  })
  return costForm
}
