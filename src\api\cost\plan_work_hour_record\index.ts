import type { PlanWorkHourRecord, PlanWorkHourRecordCreate, PlanWorkHourRecordUpdate } from './types'
import type { CommonResult, PageRequest, PageResult } from '~/api/common/types'
import { kyCreate, kyDelete, kyGet, kyPost, kyPut } from '~/utils/request'

export const planWorkHourRecordApi = {
  page: (param: PageRequest<Partial<PlanWorkHourRecord>>) => kyPost('planWorkHourRecord/page', param).json<CommonResult<PageResult<PlanWorkHourRecord>>>(),
  create: (data: PlanWorkHourRecordCreate) => kyCreate('planWorkHourRecord', data),
  update: (data: PlanWorkHourRecordUpdate) => kyPut('planWorkHourRecord', data),
  delete: (id: string) => kyDelete(`planWorkHourRecord/${id}`),
  get: (id: string) => kyGet(`planWorkHourRecord/${id}`).json<CommonResult<PlanWorkHourRecord>>(),
  getByCode: (code: string) => kyGet(`dict/getByCode/${code}`).json<PlanWorkHourRecord>(),
}
