export interface PlanWorkHourRecord {
  id: string
  departmentId: number
  departmentName?: string
  type: number
  planWorkHour: number
  date: string
  createTime?: string
  updateTime?: string
}

export interface PlanWorkHourRecordCreate {
  departmentId?: number
  departmentName?: string
  type?: number
  planWorkHour?: number
  date?: string
}

export interface PlanWorkHourRecordUpdate {
  id: string
  departmentId?: number
  departmentName?: string
  type?: number
  planWorkHour?: number
  date?: Date
}

export interface PlanWorkHourRecordEdit {
  id: string
  departmentId?: string
  departmentName?: string
  type?: number
  planWorkHour?: number
  date?: Date
}
