import type { FileRef } from './types'
import { kyApi, kyD<PERSON>te, kyGet, kyPostForm } from '~/utils/request'

export const attachmentApi = {
  upload: (file: File) => {
    const data = new FormData()
    data.set('file', file)
    return kyPostForm('attachment', data).json<FileRef>()
  },
  download: (key: string) => kyGet(`attachment/${key}`).blob(),
  head: (key: string) => kyApi.head(`attachment/${key}`),
  del: (key: string) => kyDelete(`attachment/${key}`),

}
