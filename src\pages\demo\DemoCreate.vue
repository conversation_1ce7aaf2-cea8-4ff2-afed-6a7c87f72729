<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'

import { demoApi, demoOptions, useDemoForm } from './schema'
import LTextarea from '~/components/form/label/LTextarea.vue'

const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')

const { resetForm, handleSubmit } = useDemoForm()
const { push, remove, fields } = useFieldArray('items')

const save = handleSubmit(async (values) => {
  try {
    loading.value = true
    await demoApi.create(values)
    success('创建成功')
    emits('save')
    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})

function onShow() {
  resetForm()
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="创建demo" @show="onShow">
    <form @submit="save">
      <FormLayout>
        <LInput name="name" label="名称" />
        <LTextarea name="description" label="描述" class="h-64" />
        <LDatePicker name="date1" label="日期1" />
        <LDatePicker name="date2" label="日期2" />
        <LSelect name="option1" label="选项1" :options="demoOptions" />
        <LDictSelect name="option2" label="选项2" code="SYS_TARGET" />
        <LRadioGroup name="radio" label="选项3" :options="demoOptions" />
        <LBoolRadioGroup name="yesno" label="是否" />
        <LDatePicker :date-picker-props="{ timeOnly: true }" name="time" label="时间" />
      </FormLayout>
      <Fieldset legend="子列表">
        <DataTable :value="fields" data-key="key">
          <Column header="名称">
            <template #body="slotProps">
              <SInput :name="`items[${slotProps.index}].label`" />
            </template>
          </Column>
          <Column header="选项1">
            <template #body="slotProps">
              <SSelect :name="`items[${slotProps.index}].option`" :options="demoOptions" />
            </template>
          </Column>
          <Column header="日期">
            <template #body="slotProps">
              <SDatePicker :name="`items[${slotProps.index}].date`" />
            </template>
          </Column>
          <Column header="操作">
            <template #body="slotProps">
              <Button outlined severity="danger" icon="pi pi-trash" @click="remove(slotProps.index)" />
            </template>
          </Column>
        </DataTable>
        <Button size="large" outlined fluid class="pi pi-plus mt-4" @click="push({})" />
      </Fieldset>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" :loading="loading" fluid icon="pi pi-save" label="保存" />
        <Button severity="secondary" fluid icon="pi pi-times" label="取消" @click="open = false" />
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
