<script setup lang="ts">
import type { TreeSelectProps } from 'primevue'
import type { TreeNode } from 'primevue/treenode'

defineProps<{
  name: string
  label: string
  options: TreeNode[]
  selectProps?: TreeSelectProps
}>()
</script>

<template>
  <LabelFormItem :label="label" :name="name">
    <STreeSelect :name="name" :options="options" :select-props="selectProps" />
  </LabelFormItem>
</template>
