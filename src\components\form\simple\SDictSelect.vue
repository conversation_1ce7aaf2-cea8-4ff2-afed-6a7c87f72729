<script setup lang="ts">
import type { SelectProps } from 'primevue'
import { useField } from 'vee-validate'
import { useDictStore } from '~/stores/dict'

const props = defineProps<{
  name: string
  code: string
  selectProps?: SelectProps
}>()
const store = useDictStore()
const dict = computed(() => store.get(props.code))
const { value, errorMessage } = useField<string | null | undefined>(() => props.name)
</script>

<template>
  <Select v-model="value" :options="dict?.itemList" :input-id="props.name" option-label="label" option-value="value" :invalid="errorMessage ? true : false" fluid v-bind="selectProps" />
  <ErrorMsg :error-message="errorMessage" />
</template>
