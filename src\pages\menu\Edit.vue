<script setup lang="ts">
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'

import { useEditMenuItemForm } from './schema'
import { menuApi } from '~/api/common/menu'
import type { MenuItemData } from '~/api/common/menu/types'

const props = defineProps<{
  id?: string
}>()
const emits = defineEmits<{
  save: []
}>()
const loading = ref(false)
const open = defineModel<boolean>('open')

const { resetForm, handleSubmit, setValues } = useEditMenuItemForm()

const save = handleSubmit(async (values) => {
  if (props.id) {
    try {
      loading.value = true
      await menuApi.update(props.id, values)
      success('创建成功')
      emits('save')
      open.value = false
      resetForm()
    }
    finally {
      loading.value = false
    }
  }
})

watch(open, (newValue) => {
  if (newValue === false) {
    resetForm()
  }
})

async function loadData() {
  loading.value = true
  if (props.id) {
    const menuItem: MenuItemData = await menuApi.get(props.id)
    setValues(menuItem)
    loading.value = false
  }
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="编辑菜单" @show="loadData">
    <form @submit="save">
      <FormLayout>
        <LInput name="path" label="路径" />
        <LInput name="title" label="标题" />
        <LInput name="icon" label="图标" />
        <LDictSelect name="target" label="target" code="SYS_TARGET" />
        <LInput name="access" label="权限" />
        <LInput name="parentId" label="父菜单" />
        <LInputNumber name="order" label="排序" />
        <LBoolRadioGroup name="redirect" label="重定向" />
        <LInput name="url" label="重定向地址" />
      </FormLayout>
      <div class="mt-8 flex justify-center gap-8">
        <Button :loading="loading" type="submit" fluid label="保存" />
        <Button severity="secondary" fluid label="取消" @click="open = false" />
      </div>
    </form>
    <template #footer />
  </Dialog>
</template>
