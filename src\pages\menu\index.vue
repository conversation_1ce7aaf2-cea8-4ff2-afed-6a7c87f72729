<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import Column from 'primevue/column'
import type { DataTablePageEvent } from 'primevue/datatable'
import DataTable from 'primevue/datatable'
import Button from 'primevue/button'
import ButtonGroup from 'primevue/buttongroup'
import { useForm } from 'vee-validate'
import { useConfirm } from 'primevue'
import Create from './Create.vue'
import Edit from './Edit.vue'
import { menuApi } from '~/api/common/menu'
import type { MenuItemData } from '~/api/common/menu/types'
import PageContainer from '~/components/common/PageContainer.vue'

const data = ref<MenuItemData[]>([])

const createProps = reactive<
  {
    parentId?: string
  }
>({
  parentId: '',
})

const editId = ref<string>()

const open = reactive({
  create: false,
  edit: false,
})

const loading = ref(false)

const pageData = reactive({
  pageNumber: 0,
  pageSize: 10,
})
const total = ref(0)

const confirm = useConfirm()

function confirmDel(id: string, event: any) {
  confirm.require({
    target: event.currentTarget,
    message: '确认删除？',
    accept: async () => {
      await menuApi.delete(id)
      success('删除成功')
      data.value = data.value.filter(o => o.id !== id)
    },
  })
}

const schema = toTypedSchema(z.object({
  path: z.string().optional(),
  title: z.string().optional(),
}))
const { handleSubmit } = useForm({
  validationSchema: schema,
})

const search = handleSubmit(async (values) => {
  try {
    loading.value = true
    const res = await menuApi.pageItem({ pageData, searchParams: values })
    data.value = res.list
    total.value = res.total
  }
  finally {
    loading.value = false
  }
})

function openCreatePage(parentId?: string) {
  createProps.parentId = parentId
  open.create = true
}

function page(e: DataTablePageEvent) {
  pageData.pageNumber = e.page
  pageData.pageSize = e.rows
  search()
}

function openEditPage(id: string) {
  editId.value = id
  open.edit = true
}

onMounted(() => {
  search()
})
</script>

<template>
  <PageContainer>
    <SearchBox :loading="loading" @submit="search">
      <FInput name="path" label="路径" />
      <FInput name="title" label="标题" />
    </SearchBox>
    <ButtonGroup class="pl-4">
      <Button outlined icon="pi pi-plus" @click="openCreatePage()" />
    </ButtonGroup>

    <DataTable v-model:total-records="total" lazy :rows="pageData.pageSize" class="mt-4 p-4" :value="data" paginator data-key="id" :rows-per-page-options="[10, 20]" @page="page">
      <Column field="id" header="ID" />
      <Column field="title" header="标题" />
      <Column field="path" header="路径" />
      <Column header="操作">
        <template #body="slotProps">
          <div class="flex gap-4">
            <Button outlined icon="pi pi-pencil" size="small" @click="openEditPage(slotProps.data.id)" />
            <Button outlined icon="pi pi-plus-circle" size="small" @click="openCreatePage(slotProps.data.id)" />
            <Button outlined icon="pi pi-trash" severity="danger" size="small" @click="confirmDel(slotProps.data.id, $event)" />
          </div>
        </template>
      </Column>
      <template #empty>
        <TableEmpty />
      </template>
    </DataTable>
    <Create v-model:open="open.create" :parent-id="createProps.parentId" @save="search" />
    <Edit :id="editId" v-model:open="open.edit" @save="search" />
  </PageContainer>
</template>
