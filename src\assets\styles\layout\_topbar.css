.layout-topbar {
  position: fixed;
  height: 4rem;
  z-index: 997;
  left: 0;
  top: 0;
  width: 100%;
  padding: 0 2rem;
  background-color: var(--surface-card);
  transition: left var(--layout-section-transition-duration);
  display: flex;
  align-items: center;
}

.layout-topbar .layout-topbar-logo-container {
  width: 20rem;
  display: flex;
  align-items: center;
}

.layout-topbar .layout-topbar-logo {
  display: inline-flex;
  align-items: center;
  font-size: 1.5rem;
  border-radius: var(--content-border-radius);
  color: var(--text-color);
  font-weight: 500;
  gap: 0.5rem;
}

.layout-topbar .layout-topbar-logo svg {
  width: 3rem;
}

.layout-topbar .layout-topbar-logo:focus-visible {
  outline-width: var(--focus-ring-width);
  outline-style: var(--focus-ring-style);
  outline-color: var(--focus-ring-color);
  outline-offset: var(--focus-ring-offset);
  box-shadow: var(--focus-ring-shadow);
  transition:
    box-shadow var(--transition-duration),
    outline-color var(--transition-duration);
}

.layout-topbar .layout-topbar-action {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  color: var(--text-color-secondary);
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  color: var(--text-color);
  transition: background-color var(--element-transition-duration);
  cursor: pointer;
}

.layout-topbar .layout-topbar-action:hover {
  background-color: var(--surface-hover);
}

.layout-topbar .layout-topbar-action:focus-visible {
  outline-width: var(--focus-ring-width);
  outline-style: var(--focus-ring-style);
  outline-color: var(--focus-ring-color);
  outline-offset: var(--focus-ring-offset);
  box-shadow: var(--focus-ring-shadow);
  transition:
    box-shadow var(--transition-duration),
    outline-color var(--transition-duration);
}

.layout-topbar .layout-topbar-action i {
  font-size: 1.25rem;
}

.layout-topbar .layout-topbar-action span {
  font-size: 1rem;
  display: none;
}

.layout-topbar .layout-topbar-action.layout-topbar-action-highlight {
  background-color: var(--primary-color);
  color: var(--primary-contrast-color);
}

.layout-topbar .layout-menu-button {
  margin-right: 0.5rem;
}

.layout-topbar .layout-topbar-menu-button {
  display: none;
}

.layout-topbar .layout-topbar-actions {
  margin-left: auto;
  display: flex;
  gap: 1rem;
}

.layout-topbar .layout-topbar-menu-content {
  display: flex;
  gap: 1rem;
}

.layout-topbar .layout-config-menu {
  display: flex;
  gap: 1rem;
}

@media (max-width: 991px) {
  .layout-topbar {
    padding: 0 2rem;
  }

  .layout-topbar .layout-topbar-logo-container {
    width: auto;
  }

  .layout-topbar .layout-menu-button {
    margin-left: 0;
    margin-right: 0.5rem;
  }

  .layout-topbar .layout-topbar-menu-button {
    display: inline-flex;
  }

  .layout-topbar .layout-topbar-menu {
    position: absolute;
    background-color: var(--surface-overlay);
    transform-origin: top;
    box-shadow:
      0px 3px 5px rgba(0, 0, 0, 0.02),
      0px 0px 2px rgba(0, 0, 0, 0.05),
      0px 1px 4px rgba(0, 0, 0, 0.08);
    border-radius: var(--content-border-radius);
    padding: 1rem;
    right: 2rem;
    top: 4rem;
    min-width: 15rem;
    border: 1px solid var(--surface-border);
  }

  .layout-topbar .layout-topbar-menu-content {
    gap: 0.5rem;
  }

  .layout-topbar .layout-topbar-action {
    display: flex;
    width: 100%;
    height: auto;
    justify-content: flex-start;
    border-radius: var(--content-border-radius);
    padding: 0.5rem 1rem;
  }

  .layout-topbar .layout-topbar-action i {
    font-size: 1rem;
  }

  .layout-topbar .layout-topbar-action span {
    font-weight: medium;
    display: block;
  }

  .layout-topbar .layout-topbar-menu-content {
    flex-direction: column;
  }
}

.config-panel .config-panel-label {
  font-size: 0.875rem;
  color: var(--text-secondary-color);
  font-weight: 600;
  line-height: 1;
}

.config-panel .config-panel-colors > div {
  padding-top: 0.5rem;
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  justify-content: space-between;
}

.config-panel .config-panel-colors button {
  border: none;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  padding: 0;
  cursor: pointer;
  outline-color: transparent;
  outline-width: 2px;
  outline-style: solid;
  outline-offset: 1px;
}

.config-panel .config-panel-colors button.active-color {
  outline-color: var(--primary-color);
}

.config-panel .config-panel-settings {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
