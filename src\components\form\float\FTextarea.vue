<script setup lang="ts">
import type { TextareaProps } from 'primevue'
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  label: string
  textareaProps?: TextareaProps
}>()

const { value, errorMessage } = useField<string | null | undefined>(() => props.name)
</script>

<template>
  <FFormItem :name="name" :label="label">
    <Textarea :id="props.name" v-model="value" :name="name" :invalid="errorMessage ? true : false" fluid v-bind="textareaProps" />
  </FFormItem>
</template>
