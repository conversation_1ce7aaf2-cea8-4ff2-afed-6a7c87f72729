import i18next from 'i18next'
import { z } from 'zod'
import { zodI18nMap } from 'zod-i18n-map'
// Import your language translation files
import translation from 'zod-i18n-map/locales/zh-CN/zod.json'

function attachment() {
  return z.array(z.object({
    name: z.string(),
    fileKey: z.string(),
    size: z.number(),
  }))
}

// lng and resources key depend on your locale.
i18next.init({
  lng: 'zh-CN',
  resources: {
    'zh-CN': { zod: translation },
  },
})
z.setErrorMap(zodI18nMap)

// export configured zod instance
export { z }

export const zext = {
  attachment,
}
