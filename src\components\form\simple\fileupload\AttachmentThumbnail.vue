<script setup lang="ts">
import { attachmentApi } from '~/api/common/attachment'

const props = defineProps<{
  fileKey?: string
}>()

const src = ref('')

watch(() => props.fileKey, async (newValue) => {
  if (newValue) {
    const res = await attachmentApi.download(newValue)
    const url = URL.createObjectURL(res)
    src.value = url
  }
})

onMounted(async () => {
  if (props.fileKey) {
    const res = await attachmentApi.download(props.fileKey)
    const url = URL.createObjectURL(res)
    src.value = url
  }
})
</script>

<template>
  <div class="h-20 w-20">
    <Image :src="src" v-bind="$attrs" class="h-full w-full" />
  </div>
</template>
