<script setup lang="ts">
import { useConfirm } from 'primevue'
import { attachmentApi } from '~/api/common/attachment'
import type { FileRef } from '~/api/common/attachment/types'

const props = defineProps<FileRef>()

const emits = defineEmits<{
  remove: [file: FileRef]
}>()

const confirm = useConfirm()

const url = ref<string>('')
let file = null

function onRemove(ev: any) {
  confirm.require({
    group: 'delete',
    target: ev.currentTarget,
    message: '确认删除？',
    accept: async () => {
      await attachmentApi.del(props.fileKey)
      emits('remove', props)
    },
  })
}

onMounted(async () => {
  const res = await attachmentApi.head(props.fileKey)
  const type = res.headers.get('Content-Type')
  if (type?.startsWith('image')) {
    file = await attachmentApi.download(props.fileKey)
    url.value = URL.createObjectURL(file)
  }
})

async function onDownload() {
  const a = document.createElement('a')
  if (url.value === '') {
    file = await attachmentApi.download(props.fileKey)
    a.href = URL.createObjectURL(file)
  }
  else {
    a.href = url.value
  }
  a.target = '_blank'
  a.click()
  window.URL.revokeObjectURL(a.href)
}
</script>

<template>
  <div class="flex gap-4 py-4">
    <div class="h-20 w-20 shrink-0">
      <Image :src="url" class="h-full w-full" />
    </div>

    <div class="flex grow-1 flex-col items-start justify-between gap-1">
      <div>
        {{ name }}
      </div>
      <Tag severity="success" value="Completed" class="grow-0" />
      <ProgressBar :value="100" class="w-full" />
    </div>
    <div class="flex flex-col justify-start gap-2">
      <Button outlined icon="pi pi-download" size="small" @click="onDownload" />
      <Button outlined severity="danger" icon="pi pi-trash" size="small" @click="onRemove($event)" />
    </div>
  </div>
</template>
