<script setup lang="ts">
import type { SelectProps } from 'primevue'
import { useDictStore } from '~/stores/dict'

const props = defineProps<{
  name: string
  label: string
  code: string
  selectProps?: SelectProps
}>()

const store = useDictStore()
const dict = computed(() => store.get(props.code))
const { value, errorMessage } = useField<string | null | undefined>(() => props.name)
</script>

<template>
  <FFormItem :name="name" :label="label">
    <Select v-model="value" :options="dict?.itemList" :input-id="props.name" option-label="label" option-value="value" :invalid="errorMessage ? true : false" fluid v-bind="selectProps" />
  </FFormItem>
</template>
