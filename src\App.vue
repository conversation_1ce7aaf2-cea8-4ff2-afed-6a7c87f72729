<script setup lang="ts">
import Toast from 'primevue/toast'
import ConfirmPopup from 'primevue/confirmpopup'
import { useLayoutStore } from './stores/layout'

useLayoutStore().init()
</script>

<template>
  <Toast />
  <ConfirmPopup group="delete">
    <template #container="{ message, acceptCallback, rejectCallback }">
      <div class="rounded p-4">
        <div text-lg>
          <i class="pi pi-exclamation-circle text-lg" />
          {{ message.message }}
        </div>
        <div class="mt-4 flex items-center justify-evenly gap-2">
          <Button icon="pi pi-check" severity="danger" size="small" @click="acceptCallback" />
          <Button icon="pi pi-times" outlined severity="secondary" size="small" text @click="rejectCallback" />
        </div>
      </div>
    </template>
  </ConfirmPopup>
  <RouterView />
</template>
