<script setup lang="ts">
import { VueEcharts } from 'vue3-echarts'
import { BarChart } from 'echarts/charts'
import {
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
} from 'echarts/components'
import * as echarts from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { computed } from 'vue'
import type { EChartsOption } from 'echarts'
import { formatAmount } from '~/utils/format'

const props = defineProps<{
  totalBalance: number
  totalCost: number
  periodExtraCost: number
  periodIncome: number
}>()

echarts.use([
  BarChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
  CanvasRenderer,
])

const chartOptions = computed<EChartsOption>(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    formatter: (params) => {
      if (!Array.isArray(params))
        return ''
      let result = `${params[0].name}<br/>`
      params.forEach((param) => {
        if (param.value !== null) {
          const value = typeof param.value === 'number' ? param.value : 0
          if (value === 0) {
            result += `${param.marker}${param.seriesName}: 0 元<br/>`
          }
          else {
            const formattedValue = formatAmount(Math.abs(value))
            const sign = value < 0 ? '-' : ''
            result += `${param.marker}${param.seriesName}: ${sign}${formattedValue}<br/>`
          }
        }
      })
      return result
    },
  },
  legend: {
    orient: 'horizontal',
    top: 0,
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '40px',
    containLabel: true,
  },
  yAxis: {
    type: 'category',
    data: [''],
    axisLabel: {
      color: '#71869d',
    },
  },
  xAxis: {
    type: 'value',
    axisLabel: {
      color: '#71869d',
      formatter: (value: number) => {
        if (value === 0)
          return '0 元'
        const formattedValue = formatAmount(Math.abs(value))
        const sign = value < 0 ? '-' : ''
        return `${sign}${formattedValue}`
      },
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#eee',
      },
    },
  },
  series: [
    {
      name: '结余',
      type: 'bar',
      emphasis: {
        focus: 'series',
      },
      data: [props.totalBalance],
    },
    {
      name: '人工成本',
      type: 'bar',
      stack: 'cost',
      emphasis: {
        focus: 'series',
      },
      data: [props.totalCost],
    },
    {
      name: '其他支出',
      type: 'bar',
      stack: 'cost',
      emphasis: {
        focus: 'series',
      },
      data: [props.periodExtraCost],
    },
    {
      name: '收入',
      type: 'bar',
      emphasis: {
        focus: 'series',
      },
      data: [props.periodIncome],
    },
  ],
}))
</script>

<template>
  <div>
    <VueEcharts
      :option="chartOptions"
      :autoresize="true"
      style="height: 100%; width: 100%;"
    />
  </div>
</template>
