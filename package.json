{"type": "module", "private": true, "packageManager": "pnpm@9.6.0", "scripts": {"build": "vite build", "clean": "rimraf node_modules", "dev": "vite --port 3333 --open", "lint": "eslint . --fix", "typecheck": "vue-tsc --noEmit", "preview": "vite preview", "test": "vitest", "up": "taze major -I", "prepare": "husky"}, "dependencies": {"@primevue/themes": "^4.2.5", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^10.11.1", "casdoor-vue-sdk": "^1.6.0", "i18next": "^24.2.1", "ky": "^1.7.4", "pinia": "^2.3.1", "pinia-plugin-persistedstate-2": "^2.0.28", "primeicons": "^6.0.1", "primevue": "^4.2.5", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue3-echarts": "^1.1.0", "zod": "^3.24.1", "zod-i18n-map": "^2.27.0"}, "devDependencies": {"@antfu/eslint-config": "^2.27.3", "@iconify-json/carbon": "^1.2.5", "@iconify-json/ic": "^1.2.2", "@iconify-json/ri": "^1.2.5", "@primevue/auto-import-resolver": "^4.2.5", "@types/node": "^22.10.7", "@unocss/eslint-config": "^0.61.9", "@unocss/eslint-plugin": "^0.61.9", "@unocss/preset-web-fonts": "^0.65.4", "@unocss/reset": "^0.61.9", "@vitejs/plugin-vue": "^5.2.1", "@vue/test-utils": "^2.4.6", "eslint": "^9.18.0", "eslint-plugin-format": "^0.1.3", "husky": "^9.1.7", "jsdom": "^24.1.3", "lint-staged": "^15.4.1", "pnpm": "^9.15.4", "taze": "^0.16.9", "typescript": "5.6.2", "unocss": "^0.61.9", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.27.5", "vite": "^5.4.13", "vitest": "^2.1.8", "vue-tsc": "2.0.29"}, "lint-staged": {"*": "eslint --fix"}}