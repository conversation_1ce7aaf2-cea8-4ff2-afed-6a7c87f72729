<script setup lang="ts">
import { type UploadFile, UploadStatus } from './types'

const props = defineProps<UploadFile>()

const emits = defineEmits<{
  remove: [file: UploadFile]
}>()

function onRemove() {
  emits('remove', props)
}

const tagServerity = computed(() => {
  if (props.status === UploadStatus.START) {
    return 'info'
  }
  if (props.status === UploadStatus.PENDING) {
    return 'info'
  }
  if (props.status === UploadStatus.FAILED) {
    return 'danger'
  }
  return 'primary'
})
</script>

<template>
  <div class="flex gap-4 py-4">
    <Image :src="dataUrl" class="h-20 w-20" />
    <div class="flex grow-1 flex-col items-start justify-between gap-1">
      <div>
        {{ file.name }}
      </div>
      <Tag :severity="tagServerity" :value="status" class="grow-0" />
      <ProgressBar :value="progress" class="w-full" />
    </div>
    <Button severity="danger" icon="pi pi-trash" @click="onRemove()" />
  </div>
</template>
