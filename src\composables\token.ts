import type { Jwt } from '~/utils/jwt'
import { parseJwt } from '~/utils/jwt'

export function useToken() {
  const accessTokenValue = useLocalStorage<string | undefined>(import.meta.env.VITE_APP_ACCESS_STORE, undefined)
  const refreshTokenValue = useLocalStorage<string | undefined>(import.meta.env.VITE_APP_REFRESH_STORE, undefined)
  const accessTokenExpires = useLocalStorage<number | undefined>(import.meta.env.VITE_APP_ACCESS_EXPIRES_STORE, undefined)
  const refreshTokenExpires = useLocalStorage<number | undefined>(import.meta.env.VITE_APP_REFRESH_EXPIRES_STORE, undefined)
  /***
   * accessToken expires
   */
  const accessExpire = computed(() => {
    if (!accessTokenValue.value || !accessTokenExpires.value) {
      return true
    }
    return accessTokenExpires.value - new Date().getTime() / 1000 <= 60
  })

  /***
   * refreshToken expires
   */
  const refreshExpire = computed(() => {
    if (!refreshTokenExpires.value) {
      return true
    }
    return refreshTokenExpires.value - new Date().getTime() / 1000 <= 60
  })

  const recentlyLogin = computed(() => {
    return !!refreshTokenValue.value
  })

  const canRefresh = computed(() => {
    return recentlyLogin.value && !refreshExpire.value
  })

  const accessToken = computed(() => {
    return accessTokenValue.value
  })

  const refreshToken = computed(() => {
    return refreshTokenValue.value
  })

  function setAccessToken(token: string) {
    accessTokenValue.value = token
    const jwt: Jwt = parseJwt(token)
    accessTokenExpires.value = jwt.exp
  }

  function setRefreshToken(token: string) {
    refreshTokenValue.value = token
    const jwt: Jwt = parseJwt(token)
    refreshTokenExpires.value = jwt.exp
  }

  function clear() {
    accessTokenValue.value = undefined
    refreshTokenValue.value = undefined
    accessTokenExpires.value = undefined
    refreshTokenExpires.value = undefined
  }

  return { accessToken, refreshToken, accessExpire, refreshExpire, recentlyLogin, setAccessToken, setRefreshToken, canRefresh, clear }
}
