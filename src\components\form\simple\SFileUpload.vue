<script setup lang="ts">
import type { UploadFile } from './fileupload/types'
import { UploadStatus } from './fileupload/types'
import type { FileRef } from '~/api/common/attachment/types'

const props = withDefaults(defineProps<{
  max?: number
  name: string
  accept?: string
  chooseDisabled?: boolean
  auto?: boolean
}>(), {
  max: 1,
  accept: '*',
  chooseDisabled: false,
  auto: true,
})

const { fields, push, remove } = useFieldArray<FileRef>(props.name)
const { errorMessage } = useField(props.name)
const UPLOAD_URL = '/api/attachment'

async function NewUploadFile(file: File) {
  const dataUrl = await URL.createObjectURL(file)
  return {
    uuid: crypto.randomUUID(),
    file,
    status: UploadStatus.START,
    progress: 0,
    dataUrl,
  }
}

export interface UploadedFile {
  fileRef: FileRef
}

const fileInput = ref<HTMLInputElement>()

const files = ref<UploadFile[]>([])

async function onFileSelect(_: Event) {
  const selectFiles = fileInput.value?.files
  if (!selectFiles?.length) {
    return
  }
  if (selectFiles.length + fields.value.length > props.max) {
    warn(`max file count is ${props.max}`)
    return
  }
  for (let i = 0; i < selectFiles.length; i++) {
    files.value.push(await NewUploadFile(selectFiles[i]))
  }
  if (props.auto) {
    uploadAll()
  }
}

function selectFile() {
  fileInput.value?.click()
}

async function uploadAll() {
  return Promise.allSettled(files.value.filter(o => o.status === UploadStatus.START).map(o => upload(o)))
    .then((arr) => {
      const successfullItems = arr.filter(o => o.status === 'fulfilled')
        .map(o => o.value)
      files.value = files.value.filter(o => !successfullItems.includes(o.uuid))
      const failedItems = arr.filter(o => o.status === 'rejected').map(o => o.reason)
      if (failedItems.length > 0) {
        error(failedItems.join('\n'))
      }
      if (fileInput.value) {
        fileInput.value.value = ''
      }
    })
}

async function upload(file: UploadFile): Promise<string> {
  return new Promise((resolve, reject) => {
    if (file.file) {
      const xhr = new XMLHttpRequest()
      const formData = new FormData()
      formData.set('file', file.file)

      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          file.progress = Math.round((event.loaded * 100) / event.total)
        }
      })

      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          push(JSON.parse(xhr.responseText))
          resolve(file.uuid)
        }
        else {
          file.status = UploadStatus.FAILED
          file.progress = 0
          reject(new Error(xhr.statusText))
        }
      }

      xhr.onerror = () => {
        reject(new Error('Network Error'))
      }

      xhr.open('POST', UPLOAD_URL, true)
      const accessToken = localStorage.getItem(import.meta.env.VITE_APP_ACCESS_STORE)
      if (accessToken) {
        xhr.setRequestHeader(import.meta.env.VITE_APP_AUTH_HEADER, accessToken)
      }
      xhr.send(formData)
    }
  })
}

function removeFile(file: UploadFile) {
  files.value = files.value.filter(o => o.uuid !== file.uuid)
}

function removeUploadedFile(fileRef: FileRef) {
  const idx = fields.value.findIndex(o => o.value.fileKey === fileRef.fileKey)
  remove(idx)
}

onMounted(() => {

})
</script>

<template>
  <div class="p-4 card">
    <input ref="fileInput" class="hidden" type="file" :multiple="max > 1" :accept="accept" :disabled="chooseDisabled" @change="onFileSelect">
    <SelectFileCard v-for="(file) in files" v-bind="file" :key="file.uuid" @remove="removeFile" />
    <UploadedFileCard v-for="field in fields" v-bind="field.value" :key="field.value.fileKey" @remove="removeUploadedFile" />
    <Button fluid class="mt-4 w-full" outlined icon="pi pi-upload" size="small" :disabled="chooseDisabled" @click="selectFile" />
    <ErrorMsg :error-message="errorMessage" />
  </div>
</template>
