import { toTypedSchema } from '@vee-validate/zod'
import type { PlanWorkHourRecordCreate, PlanWorkHourRecordEdit } from '~/api/cost/plan_work_hour_record/types'

// 员工类型
export enum WorkHourTypeEnum {
  FRONTLINE = 1,
  SECOND_TIER = 2,
}

export const WorkHourTypeOptions = [
  {
    label: '一线',
    value: 1,
  },
  {
    label: '二线',
    value: 2,
  },
]

export const workHourSearchSchema = toTypedSchema(
  z.object({
    departmentId: z.string().optional(),
    type: z.nativeEnum(WorkHourTypeEnum).optional(),
    date: z.date().transform((date) => {
      // 使用本地时间格式化日期，避免时区问题
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }).optional(),
  }),
)

export const workHourSchema = toTypedSchema(
  z.object({
    departmentId: z.string().min(1, '不可为空'),
    type: z.nativeEnum(WorkHourTypeEnum),
    planWorkHour: z.number().min(0, '计划工时必须大于0'),
    date: z.date().transform((date) => {
      // 使用本地时间格式化日期，避免时区问题
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }),
  }),
)

export function usePlanWorkHourCreateForm() {
  const planWorkHourForm = useForm<PlanWorkHourRecordCreate>({
    validationSchema: workHourSchema,
  })
  return planWorkHourForm
}

export function usePlanWorkHourEditEditForm() {
  const planWorkHourForm = useForm<PlanWorkHourRecordEdit>({
    validationSchema: workHourSchema,
  })
  return planWorkHourForm
}
