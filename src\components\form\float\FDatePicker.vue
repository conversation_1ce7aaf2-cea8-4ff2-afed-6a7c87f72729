<script setup lang="ts">
import type { DatePickerProps } from 'primevue'
import { useField } from 'vee-validate'
import FFormItem from './FFormItem.vue'

const props = defineProps<{
  name: string
  label: string
  dateProps?: DatePickerProps
}>()
const { value, errorMessage } = useField<Date | Array<Date> | Array<Date | null> | undefined | null>(props.name)
</script>

<template>
  <FFormItem :name="name" :label="label">
    <DatePicker v-model="value" :input-id="props.name" fluid show-icon date-format="yy-mm-dd" v-bind="dateProps" :invalid="errorMessage ? true : false">
      <template #inputicon="slotProps">
        <i class="pi pi-clock" @click="slotProps.clickCallback" />
      </template>
    </DatePicker>
  </FFormItem>
</template>
