<!-- components/MetricCard.vue -->
<script setup lang="ts">
import { computed } from 'vue'
import type { Metric } from '../types'

const props = defineProps<{
  metric: Metric
}>()

const trendClass = computed(() => ({
  'trend-up': props.metric.trend?.type === 'up',
  'trend-down': props.metric.trend?.type === 'down',
}))
</script>

<template>
  <div class="metric-card">
    <div class="metric-title">
      {{ metric.title }}
    </div>
    <div class="metric-value">
      {{ metric.value }}
    </div>

    <div v-if="metric.details" class="metric-details">
      <div
        v-for="(detail, index) in metric.details"
        :key="index"
        class="detail-item"
      >
        {{ detail }}
      </div>
    </div>

    <div
      v-if="metric.trend"
      class="metric-trend"
      :class="trendClass"
    >
      {{ metric.trend.value }}
    </div>
  </div>
</template>

<style scoped>
.metric-card {
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.metric-title {
  font-size: 0.9rem;
  color: #71869d;
  margin-bottom: 12px;
}

.metric-value {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 8px 0;
}

.metric-details {
  color: #71869d;
  line-height: 1.4;
}

.detail-item {
  margin: 4px 0;
}

.metric-trend {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.9rem;
  margin-top: 12px;
}

.trend-up {
  background: rgba(0, 217, 126, 0.1);
  color: var(--success-green);
}

.trend-down {
  background: rgba(255, 159, 67, 0.1);
  color: var(--warning-orange);
}
</style>
