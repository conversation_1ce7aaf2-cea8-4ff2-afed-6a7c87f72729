<script setup lang="ts">
import type { InputTextProps } from 'primevue'
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  label: string
  inputProps?: InputTextProps
}>()

const { value, errorMessage } = useField<string | null | undefined>(() => props.name)
</script>

<template>
  <FFormItem :name="name" :label="label">
    <InputText :id="props.name" v-model="value" :name="name" :invalid="errorMessage ? true : false" fluid v-bind="inputProps" />
  </FFormItem>
</template>
