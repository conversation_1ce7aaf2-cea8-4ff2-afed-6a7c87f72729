<!-- components/InputGroup.vue -->
<script setup lang="ts">
defineProps<{
  extraExpense: number
  cycleIncome: number
}>()

defineEmits(['update:extraExpense', 'update:cycleIncome'])
</script>

<template>
  <div class="input-group">
    <div class="grid grid-cols-[repeat(auto-fit,minmax(280px,1fr))] mx-0 my-6 gap-6">
      <div class="relative overflow-hidden border border-[var(--border-color)] rounded-[12px] border-solid p-5">
        <div class="metric-title">
          周期内收入录入
        </div>
        <input
          type="text"
          class="input-field"
          placeholder="金额（万元）"
          :value="cycleIncome"
          @input="$emit('update:cycleIncome', ($event.target as HTMLInputElement).value)"
        >
      </div>
      <div class="relative overflow-hidden border border-[var(--border-color)] rounded-[12px] border-solid p-5">
        <div class="metric-title">
          其他支出录入
        </div>
        <input
          type="text"
          class="input-field"
          placeholder="金额（万元）"
          :value="extraExpense"
          @input="$emit('update:extraExpense', ($event.target as HTMLInputElement).value)"
        >
      </div>
    </div>
  </div>
</template>

<style scoped>
.input-group {
  margin: 32px 0;
}

.input-field {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px 16px;
  width: 100%;
  transition: all 0.2s;
}

.input-field:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(44, 123, 229, 0.2);
  outline: none;
}
</style>
