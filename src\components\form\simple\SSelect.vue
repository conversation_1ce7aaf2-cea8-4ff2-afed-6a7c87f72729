<script setup lang="ts">
import type { SelectProps } from 'primevue'
import { useField } from 'vee-validate'

const props = defineProps<{
  name: string
  options: any[]
  selectProps?: SelectProps
}>()

const { value, errorMessage } = useField<string | null | undefined>(() => props.name)

const invalid = computed(() => !!errorMessage.value)
</script>

<template>
  <Select v-model="value" :input-id="props.name" :options="options" option-label="label" option-value="value" :invalid="invalid" fluid v-bind="selectProps" />
  <ErrorMsg :error-message="errorMessage" />
</template>
